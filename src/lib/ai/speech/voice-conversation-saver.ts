"use client";

import { UIMessageWithCompleted } from ".";
import { generateUUID } from "lib/utils";
import { ChatMessage, ChatMetadata } from "app-types/chat";
import { convertToSavePart } from "@/app/api/chat/shared.chat";

/**
 * Converts voice chat messages to database format and saves them as a new thread
 */
export interface SaveVoiceConversationOptions {
  messages: UIMessageWithCompleted[];
  userId: string;
  agentId?: string;
  chatModel?: {
    provider: string;
    model: string;
  };
}

/**
 * Converts UIMessageWithCompleted[] to ChatMessage[] format for database storage
 */
export function convertVoiceMessagesToChatMessages(
  messages: UIMessageWithCompleted[],
  threadId: string,
  metadata?: ChatMetadata
): Omit<ChatMessage, "createdAt">[] {
  // Filter out incomplete messages and messages with empty text
  const completedMessages = messages.filter((msg) => {
    if (!msg.completed) return false;
    
    // For text messages, ensure they have actual content
    if (msg.parts.length === 1 && msg.parts[0].type === "text") {
      const textPart = msg.parts[0] as any;
      return textPart.text && textPart.text.trim().length > 0;
    }
    
    // For tool messages, include them if they have parts
    return msg.parts.length > 0;
  });

  return completedMessages.map((message) => ({
    id: message.id,
    threadId,
    role: message.role,
    parts: message.parts.map(convertToSavePart),
    metadata,
  }));
}

/**
 * Generates a title for the voice conversation based on the first user message
 */
export function generateVoiceConversationTitle(messages: UIMessageWithCompleted[]): string {
  const firstUserMessage = messages.find(
    (msg) => msg.role === "user" && msg.completed && msg.parts.length > 0
  );
  
  if (firstUserMessage && firstUserMessage.parts[0].type === "text") {
    const textPart = firstUserMessage.parts[0] as any;
    const text = textPart.text?.trim();
    if (text) {
      // Take first 50 characters and add ellipsis if longer
      return text.length > 50 ? text.substring(0, 47) + "..." : text;
    }
  }
  
  return "Voice Conversation";
}

/**
 * Saves a voice conversation to the database
 */
export async function saveVoiceConversation(
  options: SaveVoiceConversationOptions
): Promise<{ threadId: string; title: string } | null> {
  const { messages, userId, agentId, chatModel } = options;
  
  // Don't save if there are no meaningful messages
  if (messages.length === 0) {
    return null;
  }
  
  // Check if there are any completed messages with content
  const hasContent = messages.some((msg) => {
    if (!msg.completed) return false;
    if (msg.parts.length === 0) return false;
    
    // Check for text content
    if (msg.parts[0].type === "text") {
      const textPart = msg.parts[0] as any;
      return textPart.text && textPart.text.trim().length > 0;
    }
    
    return true; // Tool messages or other types
  });
  
  if (!hasContent) {
    return null;
  }

  try {
    const threadId = generateUUID();
    const title = generateVoiceConversationTitle(messages);
    
    const metadata: ChatMetadata = {
      agentId,
      chatModel,
      toolChoice: "auto", // Default for voice conversations
    };

    // Convert messages to database format
    const chatMessages = convertVoiceMessagesToChatMessages(messages, threadId, metadata);
    
    // Save to database via API
    const response = await fetch("/api/voice-conversation", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        threadId,
        title,
        userId,
        messages: chatMessages,
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to save voice conversation: ${response.statusText}`);
    }

    const result = await response.json();
    return { threadId: result.threadId, title };
  } catch (error) {
    console.error("Error saving voice conversation:", error);
    return null;
  }
}
