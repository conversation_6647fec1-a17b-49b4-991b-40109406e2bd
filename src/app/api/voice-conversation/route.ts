import { getSession } from "auth/server";
import { chatRepository } from "lib/db/repository";
import { ChatMessage } from "app-types/chat";
import { z } from "zod";

const SaveVoiceConversationSchema = z.object({
  threadId: z.string(),
  title: z.string(),
  userId: z.string(),
  messages: z.array(z.object({
    id: z.string(),
    threadId: z.string(),
    role: z.enum(["user", "assistant", "system"]),
    parts: z.array(z.any()),
    metadata: z.any().optional(),
  })),
});

export async function POST(request: Request) {
  try {
    const session = await getSession();

    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const json = await request.json();
    const { threadId, title, userId, messages } = SaveVoiceConversationSchema.parse(json);

    // Verify the user is authorized to save this conversation
    if (userId !== session.user.id) {
      return new Response("Forbidden", { status: 403 });
    }

    // Create the thread first
    const thread = await chatRepository.insertThread({
      id: threadId,
      title,
      userId: session.user.id,
    });

    // Save all messages
    if (messages.length > 0) {
      const chatMessages: Omit<ChatMessage, "createdAt">[] = messages.map((msg) => ({
        id: msg.id,
        threadId: thread.id,
        role: msg.role,
        parts: msg.parts,
        metadata: msg.metadata,
      }));

      await chatRepository.insertMessages(chatMessages);
    }

    return Response.json({ 
      threadId: thread.id, 
      title: thread.title,
      success: true 
    });
  } catch (error: any) {
    console.error("Error saving voice conversation:", error);
    return Response.json(
      { message: error.message || "Failed to save voice conversation" },
      { status: 500 }
    );
  }
}
