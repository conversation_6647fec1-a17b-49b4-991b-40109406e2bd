1. realtime api only has a session state of 30 minutes - so around 25 minutes we need to implement a restart with all the context of the previous conversation in a new window
And when we say all the context of the existing conversation - I really mean it. Get the current conversation into a correctly formatted structure with user, assistant and retain it while you are ending the current session and starting a new session.
From the UI side - it is a must to actually continue in the same chat session - you can internally handle the previous session closing and the new session starting - but we want the history of the conversation to be perfectly visible.
The input prompt of the realtime api will also be a newer version of what we currently have for exactly this case of session ending and needing restart every 25 minutes - so that we can make it fully understand all the input data that is coming in.
We will introduce the previous chat data into the new session from the realtime api side maybe through: https://platform.openai.com/docs/guides/realtime-conversations#create-responses-outside-the-default-conversation or one of the other more suitable methods described in: https://platform.openai.com/docs/guides/realtime-conversations



Voice conversation history is now stored in the database and displayed in the left sidebar panel with microphone icons to distinguish voice conversations from text chats


